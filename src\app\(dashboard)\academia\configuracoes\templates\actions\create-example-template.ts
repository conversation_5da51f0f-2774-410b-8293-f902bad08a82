/**
 * Action para criar template de exemplo melhorado
 */

'use server';

import { createClient } from '@/services/supabase/server';
import { requireAuth } from '@/services/auth/actions/auth-actions';
import { getTenantSlug } from '@/services/tenant/';

export async function createExampleEmailTemplate() {
  try {
    // Verificar autenticação
    await requireAuth();
    
    // Obter slug do tenant atual
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      return {
        success: false,
        errors: { _form: 'Tenant não encontrado' }
      };
    }

    const supabase = await createClient();

    // Buscar ID do tenant
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError || !tenant) {
      return {
        success: false,
        errors: { _form: 'Erro ao buscar dados do tenant' }
      };
    }

    // Template de email melhorado com dados reais da academia
    const emailTemplate = `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff;">
  <!-- Cabeçalho com logo e cores da academia -->
  <div style="text-align: center; padding: 30px 20px; background-color: {{primaryColor}}; color: white;">
    {{#if academyLogo}}
    <img src="{{academyLogo}}" alt="{{academyName}}" style="max-height: 60px; margin-bottom: 15px; display: block; margin-left: auto; margin-right: auto;" />
    {{/if}}
    <h1 style="margin: 0; font-size: 28px; font-weight: bold;">{{academyName}}</h1>
    <p style="margin: 5px 0 0 0; font-size: 16px; opacity: 0.9;">Lembrete de Pagamento</p>
  </div>
  
  <!-- Conteúdo principal -->
  <div style="padding: 40px 30px; background-color: #ffffff;">
    <h2 style="color: {{secondaryColor}}; margin-top: 0; font-size: 24px;">Olá, {{studentName}}!</h2>
    
    <p style="font-size: 16px; line-height: 1.6; color: #333333; margin-bottom: 25px;">
      Esperamos que você esteja bem e aproveitando suas aulas! Este é um lembrete amigável sobre o pagamento da sua mensalidade.
    </p>
    
    <!-- Card com detalhes do pagamento -->
    <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 25px; border-radius: 12px; margin: 25px 0; border-left: 4px solid {{primaryColor}};">
      <h3 style="color: {{primaryColor}}; margin-top: 0; margin-bottom: 15px; font-size: 20px;">💳 Detalhes do Pagamento</h3>
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="padding: 8px 0; font-weight: bold; color: #555;">Plano:</td>
          <td style="padding: 8px 0; color: #333;">{{planName}}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold; color: #555;">Valor:</td>
          <td style="padding: 8px 0; color: #333; font-size: 18px; font-weight: bold;">R$ {{amount}}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold; color: #555;">Vencimento:</td>
          <td style="padding: 8px 0; color: #d63384; font-weight: bold;">{{dueDate}}</td>
        </tr>
      </table>
    </div>
    
    <p style="font-size: 16px; line-height: 1.6; color: #333333; margin-bottom: 30px;">
      Para manter sua prática em dia e continuar evoluindo, por favor efetue o pagamento até a data de vencimento.
    </p>
    
    <!-- Botão de ação -->
    <div style="text-align: center; margin: 35px 0;">
      <a href="#" style="background: linear-gradient(135deg, {{primaryColor}} 0%, {{secondaryColor}} 100%); color: white; padding: 15px 40px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block; box-shadow: 0 4px 15px rgba(0,0,0,0.2); transition: all 0.3s ease;">
        💰 Pagar Agora
      </a>
    </div>
    
    <!-- Informações adicionais -->
    <div style="background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin: 30px 0; border-left: 4px solid #2196f3;">
      <h4 style="color: #1976d2; margin-top: 0; margin-bottom: 10px;">ℹ️ Informações Importantes</h4>
      <ul style="margin: 0; padding-left: 20px; color: #333;">
        <li style="margin-bottom: 5px;">Pagamentos após o vencimento podem gerar multa</li>
        <li style="margin-bottom: 5px;">Em caso de dúvidas, entre em contato conosco</li>
        <li>Mantenha seus dados atualizados em nosso sistema</li>
      </ul>
    </div>
    
    <p style="font-size: 16px; color: #333333; margin-top: 35px; margin-bottom: 10px;">
      Dúvidas? Estamos aqui para ajudar! 🥋
    </p>
    
    <p style="font-size: 16px; color: #666666; margin-bottom: 0;">
      Atenciosamente,<br>
      <strong style="color: {{primaryColor}};">Equipe {{academyName}}</strong>
    </p>
  </div>
  
  <!-- Rodapé -->
  <div style="background-color: #f8f9fa; padding: 25px 30px; text-align: center; border-top: 1px solid #dee2e6;">
    <p style="font-size: 12px; color: #6c757d; margin: 0 0 10px 0;">
      Este email foi enviado automaticamente pelo sistema da {{academyName}}.
    </p>
    <p style="font-size: 12px; color: #6c757d; margin: 0;">
      {{academyName}} - Desenvolvendo campeões dentro e fora do tatame
    </p>
  </div>
</div>`;

    // Criar o template
    const { data: template, error: templateError } = await supabase
      .from('notification_templates')
      .insert({
        tenant_id: tenant.id,
        type: 'payment',
        channel: 'email',
        name: 'Lembrete de Pagamento - Preview Melhorado',
        subject_template: '{{academyName}} - Lembrete de Pagamento para {{studentName}}',
        body_template: emailTemplate,
        is_active: true,
        is_default: false
      })
      .select()
      .single();

    if (templateError) {
      console.error('Erro ao criar template:', templateError);
      return {
        success: false,
        errors: { _form: 'Erro ao criar template de exemplo' }
      };
    }

    return {
      success: true,
      data: template
    };

  } catch (error) {
    console.error('Erro na action createExampleEmailTemplate:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    };
  }
}
